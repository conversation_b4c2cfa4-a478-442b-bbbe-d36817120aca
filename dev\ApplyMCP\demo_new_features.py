#!/usr/bin/env python3
"""
演示新增的天气和时间功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from agent import FileOperationAgent

async def demo_new_features():
    """演示新功能"""
    print("🎉 ApplyMCP 新功能演示")
    print("=" * 50)
    print("新增功能：")
    print("🌤️ 天气查询 - 获取实时天气和预报")
    print("⏰ 时间查询 - 获取时间、日期、倒计时等")
    print("=" * 50)
    
    # 初始化代理
    agent = FileOperationAgent()
    await agent.initialize()
    
    if not agent.is_connected:
        print("❌ 无法连接到MCP服务")
        return
    
    print("✅ MCP服务连接成功")
    print()
    
    # 演示时间功能
    print("⏰ 时间功能演示:")
    print("-" * 30)
    
    time_queries = [
        "现在几点了？",
        "今天是星期几？",
        "现在是什么时段？",
        "今天的详细日期信息",
        "距离2024年12月31日还有多少天？"
    ]
    
    for query in time_queries:
        print(f"👤 用户: {query}")
        try:
            response = await agent.process_message(query)
            print(f"🤖 助手: {response}")
        except Exception as e:
            print(f"❌ 错误: {e}")
        print()
    
    # 演示天气功能
    print("🌤️ 天气功能演示:")
    print("-" * 30)
    
    weather_queries = [
        "查询北京的天气",
        "上海的天气预报",
        "广州今天天气怎么样？",
        "深圳未来3天的天气预报"
    ]
    
    for query in weather_queries:
        print(f"👤 用户: {query}")
        try:
            response = await agent.process_message(query)
            print(f"🤖 助手: {response}")
        except Exception as e:
            print(f"❌ 错误: {e}")
        print()
    
    # 演示混合功能
    print("🔄 混合功能演示:")
    print("-" * 30)
    
    mixed_queries = [
        "创建一个文件记录今天的天气",
        "把当前时间写入到time.txt文件中",
        "列出当前目录的文件"
    ]
    
    for query in mixed_queries:
        print(f"👤 用户: {query}")
        try:
            response = await agent.process_message(query)
            print(f"🤖 助手: {response}")
        except Exception as e:
            print(f"❌ 错误: {e}")
        print()
    
    print("🎊 演示完成！")
    print()
    print("💡 使用提示：")
    print("- 支持自然语言查询天气和时间")
    print("- 可以结合文件操作保存查询结果")
    print("- 天气查询支持中文城市名")
    print("- 时间查询提供多种格式和信息")
    
    # 清理
    await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(demo_new_features())
