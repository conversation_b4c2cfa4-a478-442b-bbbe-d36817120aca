#!/usr/bin/env python3
"""
测试天气和时间功能
"""

import asyncio
import sys
from mcp_client import MCPClient

async def test_weather_and_time():
    """测试天气和时间功能"""
    print("🧪 开始测试天气和时间功能...")
    print("=" * 50)
    
    # 创建MCP客户端
    client = MCPClient("file_operations_mcp.py")
    
    try:
        # 连接到服务器
        print("🔗 连接到MCP服务器...")
        if not await client.connect():
            print("❌ 连接失败")
            return
        
        print("✅ 连接成功")
        print()
        
        # 测试时间功能
        print("⏰ 测试时间功能:")
        print("-" * 30)
        
        # 获取当前时间
        try:
            time_result = await client.get_current_time("detailed")
            print("✅ 当前时间:")
            print(time_result)
            print()
        except Exception as e:
            print(f"❌ 获取当前时间失败: {e}")
        
        # 获取时间信息
        try:
            period_result = await client.get_time_info("period")
            print("✅ 时段信息:")
            print(period_result)
            print()
        except Exception as e:
            print(f"❌ 获取时段信息失败: {e}")
        
        # 获取星期信息
        try:
            weekday_result = await client.get_time_info("weekday")
            print("✅ 星期信息:")
            print(weekday_result)
            print()
        except Exception as e:
            print(f"❌ 获取星期信息失败: {e}")
        
        # 获取日期信息
        try:
            date_result = await client.get_date_info("detailed")
            print("✅ 详细日期信息:")
            print(date_result)
            print()
        except Exception as e:
            print(f"❌ 获取日期信息失败: {e}")
        
        # 测试倒计时
        try:
            countdown_result = await client.get_countdown("2024-12-31")
            print("✅ 倒计时到2024年底:")
            print(countdown_result)
            print()
        except Exception as e:
            print(f"❌ 计算倒计时失败: {e}")
        
        # 测试天气功能
        print("🌤️ 测试天气功能:")
        print("-" * 30)
        
        # 获取北京天气
        try:
            weather_result = await client.get_current_weather("北京")
            print("✅ 北京当前天气:")
            print(weather_result)
            print()
        except Exception as e:
            print(f"❌ 获取北京天气失败: {e}")
        
        # 获取上海天气预报
        try:
            forecast_result = await client.get_weather_forecast("上海", 2)
            print("✅ 上海天气预报:")
            print(forecast_result)
            print()
        except Exception as e:
            print(f"❌ 获取上海天气预报失败: {e}")
        
        print("🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_weather_and_time())
