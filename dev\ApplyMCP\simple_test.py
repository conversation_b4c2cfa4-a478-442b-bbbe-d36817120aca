#!/usr/bin/env python3
"""
简单测试新功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        from weather_service import WeatherService
        print("✅ weather_service 导入成功")
    except Exception as e:
        print(f"❌ weather_service 导入失败: {e}")
    
    try:
        from time_service import TimeService
        print("✅ time_service 导入成功")
    except Exception as e:
        print(f"❌ time_service 导入失败: {e}")
    
    try:
        from config import Config
        print("✅ config 导入成功")
        print(f"天气API密钥: {Config.WEATHER_API_KEY[:10]}...")
    except Exception as e:
        print(f"❌ config 导入失败: {e}")

def test_time_service():
    """测试时间服务"""
    print("\n⏰ 测试时间服务...")
    
    try:
        from time_service import TimeService
        time_service = TimeService()
        
        # 测试获取当前时间
        current_time = time_service.get_current_time("detailed")
        print("✅ 当前时间:")
        print(f"   {current_time['datetime']}")
        
        # 测试时间信息
        time_info = time_service.get_time_info("period")
        print("✅ 时段信息:")
        print(f"   {time_info['greeting']} - {time_info['period']}")
        
        # 测试格式化
        formatted = time_service.format_time_response(current_time, "current")
        print("✅ 格式化输出:")
        print(f"   {formatted}")
        
    except Exception as e:
        print(f"❌ 时间服务测试失败: {e}")

def test_weather_service():
    """测试天气服务"""
    print("\n🌤️ 测试天气服务...")
    
    try:
        from weather_service import WeatherService
        from config import Config
        
        weather_service = WeatherService(
            api_key=Config.WEATHER_API_KEY,
            base_url=Config.WEATHER_BASE_URL
        )
        
        print("✅ 天气服务初始化成功")
        print("⚠️ 注意：实际API调用需要网络连接")
        
        # 测试城市名翻译
        translated = weather_service._translate_city_name("北京")
        print(f"✅ 城市名翻译: 北京 -> {translated}")
        
    except Exception as e:
        print(f"❌ 天气服务测试失败: {e}")

if __name__ == "__main__":
    test_imports()
    test_time_service()
    test_weather_service()
    print("\n🎉 简单测试完成！")
